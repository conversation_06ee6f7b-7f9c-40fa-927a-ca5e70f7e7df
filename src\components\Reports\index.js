'use client';
import React, { useState, useLayoutEffect } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import CustomTabs from '@/components/UI/CustomTabs';
import { useRouter, useSearchParams } from 'next/navigation';
import { reportsMenuList } from '@/helper/common/commonMenus';
import './reports.scss';

const Reports = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMenuItem, setActiveMenuItem] = useState(1);
  const [activeTab, setActiveTab] = useState(1);

  const isReport = searchParams.get('is_report');
  const isActiveTab = searchParams.get('is_tab');
  const queryParams = new URLSearchParams(searchParams);

  // Initialize from URL parameters
  useLayoutEffect(() => {
    setActiveMenuItem(Number(isReport || 1));
  }, [isReport]);

  useLayoutEffect(() => {
    const tabIndex = Number(isActiveTab) || 1;
    setActiveTab(tabIndex);
  }, [isActiveTab]);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    setActiveTab(1); // Reset to first tab when menu item changes
    router.push(`/reports?is_report=${item?.id}&is_tab=1`);
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    queryParams.set('is_tab', tabId);
    router.push(`?${queryParams.toString()}`);
  };

  const getCurrentTabs = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );
    return currentItem?.tabs || [];
  };

  const getCurrentComponent = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );

    // If item has tabs, check for tab-specific component
    if (currentItem?.tabs && currentItem.tabs.length > 0) {
      const currentTab = currentItem.tabs.find((tab) => tab.id === activeTab);
      return currentTab?.component || currentItem?.component || null;
    }

    // If no tabs, return main component
    return currentItem?.component || null;
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        {/* Show tabs only if current menu item has tabs */}
        {getCurrentTabs().length > 0 && (
          <Box className="section-right-tab-header">
            <CustomTabs
              tabs={getCurrentTabs()}
              initialTab={activeTab}
              onTabChange={handleTabChange}
            />
          </Box>
        )}
        <Box className="section-right-content">{getCurrentComponent()}</Box>
      </Box>
    </Box>
  );
};

export default Reports;
