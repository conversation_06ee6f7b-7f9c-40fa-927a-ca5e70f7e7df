import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';

export const staffService = {
  // Get staff list with filters
  getStaffList: async (
    search = '',
    pageNo = 1,
    branch = '',
    role = '',
    department = '',
    statusValue = '',
    trainingStatus = '',
    contractStatus = '',
    rowsPerPage = 10,
    startDate = '',
    endDate = ''
  ) => {
    try {
      const startDatef = startDate
        ? dayjs(startDate)?.format('YYYY-MM-DD')
        : '';
      const endDatef = endDate ? dayjs(endDate)?.format('YYYY-MM-DD') : '';

      let url =
        URLS?.GET_USER_LIST +
        `?isAdmin=false&search=${search}&page=${pageNo}&size=${rowsPerPage}&branch_id=${branch}&status=${statusValue}&user_track_status=${trainingStatus}&contract_status=${contractStatus}&role_id=${role}&department_id=${department}`;

      // Add date filters if provided
      if (startDatef) {
        url += `&startDate=${startDatef}`;
      }
      if (endDatef) {
        url += `&endDate=${endDatef}`;
      }

      const { status, data } = await axiosInstance.get(url);

      if (status === 200) {
        return {
          success: true,
          data: {
            userList: data?.userList || [],
            count: data?.count || 0,
            page: pageNo,
          },
        };
      }
      return { success: false, data: null };
    } catch (error) {
      throw error;
    }
  },

  // Get single staff details
  getStaffDetails: async (staffId) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_USER + `${staffId}`
      );

      if (status === 200) {
        return { success: true, data: data?.data };
      }
      return { success: false, data: null };
    } catch (error) {
      throw error;
    }
  },

  // Delete staff member
  deleteStaff: async (staffId) => {
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_USER + staffId
      );

      if (status === 200) {
        return { success: true, message: data?.message };
      }
      return { success: false, message: 'Failed to delete staff' };
    } catch (error) {
      throw error;
    }
  },

  // Export staff list
  exportStaffList: async (
    search = '',
    pageNo = 1,
    branch = '',
    role = '',
    department = '',
    statusValue = '',
    trainingStatus = '',
    contractStatus = '',
    rowsPerPage = 10,
    fileType = 'csv',
    totalCount = 0
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.EXPORT_USER_LIST +
          `?isAdmin=false&search=${search}&page=1&size=${totalCount}&branch_id=${branch}&status=${statusValue}&user_track_status=${trainingStatus}&contract_status=${contractStatus}&role_id=${role}&department_id=${department}&file_type=${fileType}`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        return { success: true, data };
      }
      return { success: false, data: null };
    } catch (error) {
      throw error;
    }
  },

  // Get branch list for filters
  getBranchList: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST + `?search=&page=1&size=&branchStatus=active`
      );

      if (status === 200) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterBranchList = data?.data?.map((branch) => ({
          label: branch?.branch_name,
          value: branch?.id,
        }));
        return [...alloption, ...filterBranchList];
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  // Get department list for filters
  getDepartmentList: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST + `?search=&page=1&size=&departmentStatus=active`
      );

      if (status === 200) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterDepartmentList = data?.data?.map((department) => ({
          label: department?.department_name,
          value: department?.id,
        }));
        return [...alloption, ...filterDepartmentList];
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  // Get role list for filters
  getRoleList: async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

      if (status === 200) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterRoleList = data?.data?.map((role) => ({
          label: role?.role_name,
          value: role?.id,
        }));
        return [...alloption, ...filterRoleList];
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  // Create staff member
  createStaff: async (staffData) => {
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.CREATE_USER,
        staffData
      );

      if (status === 200) {
        return { success: true, data: data?.data, message: data?.message };
      }
      return { success: false, message: 'Failed to create staff' };
    } catch (error) {
      throw error;
    }
  },

  // Update staff member
  updateStaff: async (staffId, staffData) => {
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.UPDATE_USER + staffId,
        staffData
      );

      if (status === 200) {
        return { success: true, data: data?.data, message: data?.message };
      }
      return { success: false, message: 'Failed to update staff' };
    } catch (error) {
      throw error;
    }
  },

  // Send staff invitation
  sendStaffInvitation: async (staffId) => {
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.SEND_INVITATION,
        { user_id: staffId }
      );

      if (status === 200) {
        return { success: true, message: data?.message };
      }
      return { success: false, message: 'Failed to send invitation' };
    } catch (error) {
      throw error;
    }
  },

  // Get staff fields configuration
  getStaffFields: async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_USER_FIELDS);

      if (status === 200) {
        return { success: true, data: data?.data };
      }
      return { success: false, data: null };
    } catch (error) {
      throw error;
    }
  },

  // Store staff field sequence
  storeStaffFieldSequence: async (fieldOrder) => {
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.STORE_USER_FIELDS_SEQUENCE,
        { user_field_order: fieldOrder }
      );

      if (status === 200) {
        return { success: true, message: data?.message };
      }
      return { success: false, message: 'Failed to store field sequence' };
    } catch (error) {
      throw error;
    }
  },
};
