'use client';
import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography } from '@mui/material';

import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { staticOptions } from '@/helper/common/staticOptions';
import Icon from '@/components/UI/AppIcon/AppIcon';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import { staffService } from '@/services/staffService';
import { saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import AddIcon from '@mui/icons-material/Add';
import DownloadIcon from '@mui/icons-material/Download';
import SettingsIcon from '@mui/icons-material/Settings';
import { Popover, Tooltip } from '@mui/material';
import ExportStatusIndicator from '@/components/Users/<USER>/DownloadField/components/ExportProgress';

import CustomButton from '@/components/UI/CustomButton';
import { useRouter, useSearchParams } from 'next/navigation';
import CreateStaff from '@/components/Users/<USER>';
import UserProfile from '@/components/Users/<USER>';

import '../reports.scss';
import '../../Users/<USER>/staff.scss';

// Filter fields for staff reports
const filterFields = [
  {
    type: 'search',
    label: 'Search',
    name: 'search',
    placeholder: 'Search by name, email, or employment number',
  },
  {
    type: 'select',
    label: 'Branch',
    name: 'branch',
    placeholder: 'Select Branch',
    options: [], // Will be populated dynamically
  },
  {
    type: 'select',
    label: 'Department',
    name: 'department',
    placeholder: 'Select Department',
    options: [], // Will be populated dynamically
  },
  {
    type: 'select',
    label: 'Role',
    name: 'role',
    placeholder: 'Select Role',
    options: [], // Will be populated dynamically
  },
  {
    type: 'select',
    label: 'Status',
    name: 'status',
    placeholder: 'Select Status',
    options: staticOptions.USER_STATUS_OPTIONS,
  },
  {
    type: 'select',
    label: 'Training Status',
    name: 'trainingStatus',
    placeholder: 'Select Training Status',
    options: staticOptions.TRAINING_FILTER_STATUS,
  },
  {
    type: 'select',
    label: 'Contract Status',
    name: 'contractStatus',
    placeholder: 'Select Contract Status',
    options: staticOptions.CONTRACT_FILTER_STATUS,
  },
  {
    type: 'date-range',
    label: 'Date Range',
    name: 'dateRange',
    placeholder: 'Select date range',
    format: 'MMM dd, yyyy',
  },
];

// Status class functions
const getStatusClass = (status) => {
  const map = {
    rejected: 'failed',
    deleted: 'failed',
    cancelled: 'failed',
    ongoing: 'ongoing',
    pending: 'draft',
    active: 'active-onboarding',
    completed: 'active-onboarding',
    verified: 'active-onboarding',
  };
  return map[status] || 'success';
};

const getStatusClassOnboard = (status) => {
  const map = {
    ongoing: 'ongoing',
    pending: 'draft',
    completed: 'active-onboarding',
  };
  return map[status] || 'success';
};

export default function StaffUserReports() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isEdit = searchParams.get('is_edit');
  const staffId = searchParams.get('staff_id');
  const { authState, setUserdata, setRestrictedLimitModal } =
    useContext(AuthContext);
  const queryParams = new URLSearchParams(searchParams);

  const [staffList, setStaffList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [filterFieldsWithOptions, setFilterFieldsWithOptions] =
    useState(filterFields);

  // Export functionality state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  // Export status states (similar to original Staff component)
  const [exportStatus, setExportStatus] = useState('idle');
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFileName, setExportFileName] = useState('');
  const [exportError, setExportError] = useState('');
  const [downloadUrl, setDownloadUrl] = useState('');
  const [currentExportType, setCurrentExportType] = useState('');
  const [selectedFields, setSelectedFields] = useState([]);

  // Filter states to match original Staff component
  const [filterData, setFilterData] = useState({
    branch: '',
    role: '',
    department: '',
    status: '',
    trainingStatus: '',
    contractStatus: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    role: '',
    department: '',
    status: '',
    trainingStatus: '',
    contractStatus: '',
  });

  // Selection state to match original Staff component export condition
  const [selectedCat, setSelectedCat] = useState('all'); // Default to 'all' for reports context

  // Create staff functionality state
  const [showCreateStaff, setShowCreateStaff] = useState(false);

  // Override router behavior when in create staff mode
  useEffect(() => {
    if (showCreateStaff) {
      const originalPush = router.push;
      const originalBack = router.back;

      router.push = (url) => {
        if (url === '/staff') {
          // Instead of navigating to /staff, close the form and refresh the list
          setShowCreateStaff(false);
          getStaffList();
          return Promise.resolve(true);
        }
        return originalPush.call(router, url);
      };

      router.back = () => {
        // Instead of going back, close the form and refresh the list
        setShowCreateStaff(false);
        getStaffList();
      };

      return () => {
        router.push = originalPush;
        router.back = originalBack;
      };
    }
  }, [showCreateStaff, router]);

  // Menu items for action dropdown - will be replaced with proper action icons
  const actionMenuItems = [];

  // Add Edit and Delete options based on permissions
  if (authState?.UserPermission?.staff === 2) {
    actionMenuItems.push(
      {
        label: 'Edit',
        icon: <Icon name="Edit" size={16} />,
        onClick: (_, row) => {
          // Handle edit action - use URL parameters like branch component
          setUserdata(null);
          setUserdata({
            id: row?.id,
            filterData: filters,
            searchValue: searchValue,
            page: page,
            rowperpage: rowsPerPage,
          });
          saveToStorage(identifiers?.RedirectData, {
            id: row?.id,
            filterData: filters,
            searchValue: searchValue,
            page: page,
            rowperpage: rowsPerPage,
          });
          // Set URL parameters like branch component
          queryParams.set('is_edit', 'true');
          queryParams.set('staff_id', row?.id);
          router.push(`?${queryParams.toString()}`);
        },
      },
      {
        label: 'Delete',
        icon: <Icon name="Trash2" size={16} />,
        variant: 'danger',
        onClick: (_, row) => {
          setDeleteId(row?.id);
          setDeleteDialogOpen(true);
        },
      }
    );
  }

  // CommonTable columns
  const columns = [
    {
      header: 'ID',
      accessor: 'employment_number',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'User',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          setUserdata={setUserdata}
          authState={authState}
          navigationProps={{ staff: true }}
        />
      ),
    },
    {
      header: 'Branch / Dep.',
      accessor: 'branch_department',
      sortable: false,
      renderCell: (_, row) => <BranchDepartmentDisplay row={row} />,
    },
    {
      header: 'Joining Date',
      accessor: 'user_joining_date',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          {DateFormat(value, 'date')}
        </Box>
      ),
    },
    {
      header: 'Profile Status',
      accessor: 'user_status',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100 text-capital">
          <Typography
            className={`sub-title-text ${getStatusClass(value)} fw600`}
          >
            {value}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Training Status',
      accessor: 'user_track_status',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100 text-capital">
          <Typography
            className={`sub-title-text ${getStatusClassOnboard(value)} fw600`}
          >
            {value}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Contract Status',
      accessor: 'user_contract',
      sortable: false,
      renderCell: (_, row) => {
        const contractStatus =
          row?.user_contract?.is_confirm_sign === 1
            ? row?.is_probation === 1
              ? 'Probation'
              : 'Active'
            : row?.user_contract?.is_confirm_sign === 0
              ? 'Awaiting Signature'
              : 'Pending';

        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            <Typography
              className={`sub-title-text ${getStatusClass(contractStatus?.toLowerCase())} fw600`}
            >
              {contractStatus}
            </Typography>
          </Box>
        );
      },
    },
  ];

  // Load filter options
  const loadFilterOptions = async () => {
    try {
      const [branches, departments, roles] = await Promise.all([
        staffService.getBranchList(),
        staffService.getDepartmentList(),
        staffService.getRoleList(),
      ]);

      // Update filter fields with options
      const updatedFields = filterFieldsWithOptions.map((field) => {
        if (field.name === 'branch') {
          return { ...field, options: branches };
        }
        if (field.name === 'department') {
          return { ...field, options: departments };
        }
        if (field.name === 'role') {
          return { ...field, options: roles };
        }
        return field;
      });

      setFilterFieldsWithOptions(updatedFields);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  // Get staff list from API
  const getStaffList = async (
    search = '',
    pageNo = 1,
    branch = '',
    role = '',
    department = '',
    statusValue = '',
    trainingStatus = '',
    contractStatus = '',
    Rpp = rowsPerPage,
    startDate = '',
    endDate = ''
  ) => {
    setLoader(true);
    try {
      let url =
        URLS?.GET_USER_LIST +
        `?isAdmin=false&search=${search}&page=${pageNo}&size=${Rpp}&branch_id=${branch}&status=${statusValue}&user_track_status=${trainingStatus}&contract_status=${contractStatus}&role_id=${role}&department_id=${department}`;

      // Add date range if provided
      if (startDate) url += `&start_date=${startDate}`;
      if (endDate) url += `&end_date=${endDate}`;

      const { status, data } = await axiosInstance.get(url);

      if (status === 200) {
        setStaffList(data?.userList || []);
        setTotalCount(data?.count || 0);
        setPage(pageNo);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setStaffList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handle filter application
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values?.search || '');

    const startDate = values?.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values?.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    setPage(1);
    getStaffList(
      values?.search || '',
      1,
      values?.branch || '',
      values?.role || '',
      values?.department || '',
      values?.status || '',
      values?.trainingStatus || '',
      values?.contractStatus || '',
      rowsPerPage,
      startDate,
      endDate
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getStaffList(
      searchValue,
      newPage,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      rowsPerPage,
      startDate,
      endDate
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getStaffList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      newRowsPerPage,
      startDate,
      endDate
    );
  };

  // Delete staff member
  const deleteStaff = async (id) => {
    try {
      setLoader(true);
      const result = await staffService.deleteStaff(id);

      if (result.success) {
        setApiMessage('success', result.message);
        handleCloseDeleteDialog();

        // Handle pagination if this was the last item on the page
        if (staffList?.length === 1 && page !== 1) {
          setPage(page - 1);
          const startDate = filters?.dateRange?.[0]
            ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
            : '';
          const endDate = filters?.dateRange?.[1]
            ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
            : '';

          getStaffList(
            searchValue,
            page - 1,
            filters?.branch || '',
            filters?.role || '',
            filters?.department || '',
            filters?.status || '',
            filters?.trainingStatus || '',
            filters?.contractStatus || '',
            rowsPerPage,
            startDate,
            endDate
          );
        } else {
          // Refresh current page
          const startDate = filters?.dateRange?.[0]
            ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
            : '';
          const endDate = filters?.dateRange?.[1]
            ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
            : '';

          getStaffList(
            searchValue,
            page,
            filters?.branch || '',
            filters?.role || '',
            filters?.department || '',
            filters?.status || '',
            filters?.trainingStatus || '',
            filters?.contractStatus || '',
            rowsPerPage,
            startDate,
            endDate
          );
        }
      } else {
        setApiMessage('error', 'Failed to delete staff member');
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Export download function (similar to original Staff component)
  const getDownloadUserList = async (
    search,
    pageNo,
    branch,
    role,
    department,
    statusValue,
    trainingStatus,
    contractStatus,
    Rpp,
    fileType,
    isExport
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.EXPORT_USER_LIST +
          `?isAdmin=false&search=${search}&page=1&size=${totalCount}&branch_id=${branch ? branch : ''}&status=${
            statusValue ? statusValue : ''
          }&user_track_status=${
            trainingStatus ? trainingStatus : ''
          }&contract_status=${contractStatus ? contractStatus : ''}&role_id=${
            role ? role : ''
          }&department_id=${department ? department : ''}&file_type=${
            fileType ? fileType : 'csv'
          }`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        setExportProgress(90);

        const url = window.URL.createObjectURL(new Blob([data]));
        const filename = `${identifiers?.APP_NAME}_Staff_List.${fileType === 'excel' ? 'xlsx' : 'csv'}`;

        // Store download URL for the download button
        setDownloadUrl(url);
        setExportFileName(filename);
        setExportProgress(100);
        setExportStatus('completed');

        isExport && getStoredField();
      }
    } catch (error) {
      setExportStatus('error');
      setExportError(error?.response?.data?.message || 'Export failed');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Get stored field configuration
  const getStoredField = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STORED_USER_FIELDS
      );
      if (status === 200) {
        setSelectedFields(data?.data?.user_field_order || []);
      }
    } catch (error) {
      console.error('Error getting stored fields:', error);
    }
  };

  // Handle save configuration
  const handleSaveConfiguration = (exportType) => {
    setCurrentExportType(exportType);
    // This would typically open a configuration modal
    // For now, we'll just trigger the export with default fields
    if (selectedFields?.length > 0) {
      setExportStatus('processing');
      setExportProgress(10);
      setExportFileName(
        `staff_list.${exportType === 'excel' ? 'xlsx' : 'csv'}`
      );
      setCurrentExportType(exportType);
      setExportError('');

      getDownloadUserList(
        searchValue,
        1,
        filterData?.branch,
        filterData?.role,
        filterData?.department,
        filterData?.status,
        filterData?.trainingStatus,
        filterData?.contractStatus,
        '',
        exportType,
        true
      );
    }
  };

  // Export status handlers
  const handleExportDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', exportFileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportDismiss = () => {
    setExportStatus('idle');
    setExportProgress(0);
    setExportFileName('');
    setExportError('');
    setDownloadUrl('');
    setCurrentExportType('');
  };

  const handleExportRetry = () => {
    if (currentExportType) {
      if (selectedFields?.length > 0) {
        getDownloadUserList(
          searchValue,
          1,
          filterData?.branch,
          filterData?.role,
          filterData?.department,
          filterData?.status,
          filterData?.trainingStatus,
          filterData?.contractStatus,
          '',
          currentExportType,
          true
        );
      } else {
        handleSaveConfiguration(currentExportType);
      }
    }
  };

  // Export handlers
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // Dialog handlers
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  // useEffect hooks
  useEffect(() => {
    loadFilterOptions();
    getStoredField(); // Load stored field configuration for export
  }, []);

  useEffect(() => {
    getStaffList();
  }, []);

  // Cleanup download URL when component unmounts or export status changes
  useEffect(() => {
    return () => {
      if (downloadUrl) {
        URL.revokeObjectURL(downloadUrl);
      }
    };
  }, [downloadUrl]);

  return (
    <Box className="report-main-container">
      {showCreateStaff ? (
        <Box className="reports-create-staff-wrapper">
          <CreateStaff />
        </Box>
      ) : isEdit === 'true' ? (
        <Box className="reports-edit-staff-wrapper">
          <UserProfile key={staffId} params={{ id: staffId }} />
        </Box>
      ) : (
        <>
          <Box className="report-header d-flex justify-end align-center ">
            {/* Action buttons section */}
            <Box className="d-flex align-center gap-10">
              <Box className="d-flex align-center gap-5">
                {authState?.UserPermission?.staff === 2 && (
                  <CustomButton
                    title="Create staff"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      if (authState?.remaining_emp === 0) {
                        setRestrictedLimitModal('staff-list');
                      } else {
                        setShowCreateStaff(true);
                      }
                    }}
                  />
                )}
                {authState?.UserPermission?.staff === 2 && (
                  <>
                    <CustomButton
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={<Typography>Export</Typography>}
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                          arrow
                        >
                          <DownloadIcon />
                        </Tooltip>
                      }
                      disabled={
                        selectedCat !== 'all' && selectedCat?.length === 0
                      }
                      onClick={handleClick}
                    />
                    <Popover
                      className="export-popover"
                      id={id}
                      open={open}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'left',
                      }}
                    >
                      <Box className="export-option">
                        <Typography
                          className="title-text fw600 pb8 cursor-pointer"
                          onClick={() => {
                            setPage(1);
                            setFilterDataApplied({
                              branch: filters?.branch,
                              role: filters?.role,
                              department: filters?.department,
                              status: filters?.status,
                              trainingStatus: filters?.trainingStatus,
                              contractStatus: filters?.contractStatus,
                            });
                            if (selectedFields?.length > 0) {
                              // Set export status for direct download
                              setExportStatus('processing');
                              setExportProgress(10);
                              setExportFileName('staff_list.xlsx');
                              setCurrentExportType('excel');
                              setExportError('');

                              getDownloadUserList(
                                searchValue,
                                1,
                                filters?.branch,
                                filters?.role,
                                filters?.department,
                                filters?.status,
                                filters?.trainingStatus,
                                filters?.contractStatus,
                                '',
                                'excel',
                                true
                              );
                            } else {
                              handleSaveConfiguration('excel');
                            }
                            handleClose(); // Close the popover
                          }}
                        >
                          Excel
                        </Typography>
                        <Typography
                          className="title-text fw600 cursor-pointer"
                          onClick={() => {
                            setPage(1);
                            setFilterDataApplied({
                              branch: filters?.branch,
                              role: filters?.role,
                              department: filters?.department,
                              status: filters?.status,
                              trainingStatus: filters?.trainingStatus,
                              contractStatus: filters?.contractStatus,
                            });
                            if (selectedFields?.length > 0) {
                              // Set export status for direct download
                              setExportStatus('processing');
                              setExportProgress(10);
                              setExportFileName('staff_list.csv');
                              setCurrentExportType('csv');
                              setExportError('');

                              getDownloadUserList(
                                searchValue,
                                1,
                                filters?.branch,
                                filters?.role,
                                filters?.department,
                                filters?.status,
                                filters?.trainingStatus,
                                filters?.contractStatus,
                                '',
                                'csv',
                                true
                              );
                            } else {
                              handleSaveConfiguration('csv');
                            }
                            handleClose(); // Close the popover
                          }}
                        >
                          CSV
                        </Typography>
                      </Box>
                    </Popover>

                    <CustomButton
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={<Typography>Export Settings</Typography>}
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                          arrow
                        >
                          <SettingsIcon />
                        </Tooltip>
                      }
                      onClick={() => {
                        router.push('/org/setup?is_setup=8&is_tab=1');
                      }}
                    />
                  </>
                )}
              </Box>

              {/* <Box className="export-section">
                {authState?.UserPermission?.staff === 2 && (
                  <>
                    <CustomButton
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={<Typography>Export</Typography>}
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                          arrow
                        >
                          <DownloadIcon />
                        </Tooltip>
                      }
                      disabled={
                        selectedCat !== 'all' && selectedCat?.length === 0
                      }
                      onClick={handleClick}
                    />
                    <Popover
                      className="export-popover"
                      id={id}
                      open={open}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'left',
                      }}
                    >
                      <Box className="export-option">
                        <Typography
                          className="title-text fw600 pb8 cursor-pointer"
                          onClick={() => {
                            setPage(1);
                            setFilterDataApplied({
                              branch: filters?.branch,
                              role: filters?.role,
                              department: filters?.department,
                              status: filters?.status,
                              trainingStatus: filters?.trainingStatus,
                              contractStatus: filters?.contractStatus,
                            });
                            if (selectedFields?.length > 0) {
                              // Set export status for direct download
                              setExportStatus('processing');
                              setExportProgress(10);
                              setExportFileName('staff_list.xlsx');
                              setCurrentExportType('excel');
                              setExportError('');

                              getDownloadUserList(
                                searchValue,
                                1,
                                filters?.branch,
                                filters?.role,
                                filters?.department,
                                filters?.status,
                                filters?.trainingStatus,
                                filters?.contractStatus,
                                '',
                                'excel',
                                true
                              );
                            } else {
                              handleSaveConfiguration('excel');
                            }
                            handleClose(); // Close the popover
                          }}
                        >
                          Excel
                        </Typography>
                        <Typography
                          className="title-text fw600 cursor-pointer"
                          onClick={() => {
                            setPage(1);
                            setFilterDataApplied({
                              branch: filters?.branch,
                              role: filters?.role,
                              department: filters?.department,
                              status: filters?.status,
                              trainingStatus: filters?.trainingStatus,
                              contractStatus: filters?.contractStatus,
                            });
                            if (selectedFields?.length > 0) {
                              // Set export status for direct download
                              setExportStatus('processing');
                              setExportProgress(10);
                              setExportFileName('staff_list.csv');
                              setCurrentExportType('csv');
                              setExportError('');

                              getDownloadUserList(
                                searchValue,
                                1,
                                filters?.branch,
                                filters?.role,
                                filters?.department,
                                filters?.status,
                                filters?.trainingStatus,
                                filters?.contractStatus,
                                '',
                                'csv',
                                true
                              );
                            } else {
                              handleSaveConfiguration('csv');
                            }
                            handleClose(); // Close the popover
                          }}
                        >
                          CSV
                        </Typography>
                      </Box>
                    </Popover>

                    <CustomButton
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={<Typography>Export Settings</Typography>}
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                          arrow
                        >
                          <SettingsIcon />
                        </Tooltip>
                      }
                      onClick={() => {
                        router.push('/org/setup?is_setup=8&is_tab=1');
                      }}
                    />
                  </>
                )}
              </Box> */}
            </Box>
          </Box>

          <FilterCollapse
            fields={filterFieldsWithOptions}
            onApply={handleApplyFilters}
            initialValues={filters}
          />

          {loader ? (
            <ContentLoader />
          ) : staffList?.length > 0 ? (
            <CommonTable
              columns={columns}
              data={staffList}
              totalCount={totalCount}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              actionMenuItems={actionMenuItems}
              showPagination={true}
            />
          ) : (
            <NoDataView
              title="No Staff Found"
              description="No staff members match your current filters."
            />
          )}

          {/* Delete Confirmation Dialog */}
          <DialogBox
            open={deleteDialogOpen}
            handleClose={handleCloseDeleteDialog}
            title="Confirmation"
            className="delete-modal"
            dividerClass="delete-modal-divider"
            content={
              <DeleteModal
                handleCancel={handleCloseDeleteDialog}
                handleConfirm={() => deleteStaff(deleteId)}
                text="Are you sure you want to delete this staff member?"
              />
            }
          />
        </>
      )}

      <ExportStatusIndicator
        status={exportStatus}
        progress={exportProgress}
        fileName={exportFileName}
        onDownload={handleExportDownload}
        onDismiss={handleExportDismiss}
        onRetry={handleExportRetry}
        exportError={exportError}
        estimatedTime={exportStatus === 'processing' ? '1-2 minutes' : null}
      />
    </Box>
  );
}
