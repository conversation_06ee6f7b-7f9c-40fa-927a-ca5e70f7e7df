'use client';
import React, { useState } from 'react';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { statusClassName } from '@/helper/common/commonFunctions';

const departmentOptions = [
  { label: 'Select Department', value: '' },
  { label: 'HR', value: 'hr' },
  { label: 'Finance', value: 'finance' },
  { label: 'Operations', value: 'operations' },
];

const userOptions = [
  { label: 'Select User', value: '' },
  { label: '<PERSON>', value: 'john' },
  { label: '<PERSON>', value: 'jane' },
];

const periodOptions = [
  { label: 'Week', value: 'week' },
  { label: 'Month', value: 'month' },
  { label: 'Custom', value: 'custom' },
];

const columns = [
  { header: 'Employee', accessor: 'employee', sortable: true },
  { header: 'Department', accessor: 'department', sortable: true },
  { header: 'Shift', accessor: 'shift', sortable: true },
  { header: 'Date', accessor: 'date', sortable: true },
  { header: 'Hours', accessor: 'hours', sortable: false },
  {
    header: 'Status',
    accessor: 'status',
    sortable: true,
    renderCell: (value) => {
      return (
        <>
          <span className={`${statusClassName(value)}`}>{value}</span>
        </>
      );
    },
  },
];

const staticData = [
  {
    employee: 'John Smith',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-15',
    hours: '9:00 - 17:00',
    status: 'success',
  },
  {
    employee: 'Sarah Johnson',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-15',
    hours: '14:00 - 22:00',
    status: 'completed',
  },
  {
    employee: 'Mike Wilson',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-16',
    hours: '22:00 - 06:00',
    status: 'success',
  },
  {
    employee: 'Emma Davis',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-16',
    hours: '8:00 - 16:00',
    status: 'cancelled',
  },
  {
    employee: 'David Lee',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-17',
    hours: '14:00 - 22:00',
    status: 'success',
  },
  {
    employee: 'Olivia Brown',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-17',
    hours: '22:00 - 06:00',
    status: 'completed',
  },
  {
    employee: 'James Miller',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-18',
    hours: '9:00 - 17:00',
    status: 'success',
  },
  {
    employee: 'Sophia Wilson',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-18',
    hours: '14:00 - 22:00',
    status: 'cancelled',
  },
  {
    employee: 'Benjamin Clark',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-19',
    hours: '22:00 - 06:00',
    status: 'success',
  },
  {
    employee: 'Ava Martinez',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-19',
    hours: '8:00 - 16:00',
    status: 'completed',
  },
  {
    employee: 'William Harris',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-20',
    hours: '14:00 - 22:00',
    status: 'success',
  },
  {
    employee: 'Mia Lewis',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-20',
    hours: '22:00 - 06:00',
    status: 'success',
  },
  {
    employee: 'Elijah Walker',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-21',
    hours: '9:00 - 17:00',
    status: 'completed',
  },
  {
    employee: 'Charlotte Hall',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-21',
    hours: '14:00 - 22:00',
    status: 'success',
  },
  {
    employee: 'Lucas Allen',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-22',
    hours: '22:00 - 06:00',
    status: 'cancelled',
  },
  {
    employee: 'Amelia Young',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-22',
    hours: '8:00 - 16:00',
    status: 'success',
  },
  {
    employee: 'Henry King',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-23',
    hours: '14:00 - 22:00',
    status: 'completed',
  },
  {
    employee: 'Emily Wright',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-23',
    hours: '22:00 - 06:00',
    status: 'success',
  },
  {
    employee: 'Jack Scott',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-24',
    hours: '9:00 - 17:00',
    status: 'success',
  },
  {
    employee: 'Grace Green',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-24',
    hours: '14:00 - 22:00',
    status: 'completed',
  },
  {
    employee: 'Daniel Adams',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-25',
    hours: '22:00 - 06:00',
    status: 'success',
  },
  {
    employee: 'Ella Baker',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-25',
    hours: '8:00 - 16:00',
    status: 'cancelled',
  },
  {
    employee: 'Matthew Nelson',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-26',
    hours: '14:00 - 22:00',
    status: 'success',
  },
  {
    employee: 'Scarlett Carter',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-26',
    hours: '22:00 - 06:00',
    status: 'completed',
  },
  {
    employee: 'Sebastian Perez',
    department: 'Sales',
    shift: 'Morning',
    date: '2024-01-27',
    hours: '9:00 - 17:00',
    status: 'success',
  },
  {
    employee: 'Chloe Roberts',
    department: 'Marketing',
    shift: 'Evening',
    date: '2024-01-27',
    hours: '14:00 - 22:00',
    status: 'success',
  },
  {
    employee: 'Alexander Turner',
    department: 'IT',
    shift: 'Night',
    date: '2024-01-28',
    hours: '22:00 - 06:00',
    status: 'completed',
  },
  {
    employee: 'Lily Phillips',
    department: 'HR',
    shift: 'Morning',
    date: '2024-01-28',
    hours: '8:00 - 16:00',
    status: 'success',
  },
  {
    employee: 'Mason Campbell',
    department: 'Finance',
    shift: 'Evening',
    date: '2024-01-29',
    hours: '14:00 - 22:00',
    status: 'cancelled',
  },
  {
    employee: 'Zoe Parker',
    department: 'Operations',
    shift: 'Night',
    date: '2024-01-29',
    hours: '22:00 - 06:00',
    status: 'success',
  },
];

// Menu items for each row
const menuItems = [
  {
    label: 'View',
    icon: <Icon name="Eye" size={16} />,
    onClick: (item, row) => {
      alert(`View details for ${row.employee}`);
      // Add navigation or modal logic here
    },
  },
  {
    label: 'Edit',
    icon: <Icon name="Edit3" size={16} />,
    onClick: (item, row) => {
      alert(`Edit ${row.employee}`);
      // Add edit logic here
    },
  },
  {
    label: 'Delete',
    icon: <Icon name="Trash2" size={16} />,
    variant: 'danger',
    onClick: (item, row) => {
      alert(`Delete ${row.employee}`);
      // Add delete logic here
    },
  },
];

export default function RotaReports() {
  const [filters, setFilters] = useState({ period: 'week' });
  const [filteredData, setFilteredData] = useState(staticData);

  // Helper functions to get week/month date range
  const getCurrentWeekRange = () => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 (Sun) - 6 (Sat)
    const diffToMonday = (dayOfWeek + 6) % 7;
    const monday = new Date(now);
    monday.setDate(now.getDate() - diffToMonday);
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);
    return [monday, sunday];
  };
  const getCurrentMonthRange = () => {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    return [firstDay, lastDay];
  };

  // Dynamically build filter fields
  const filterFields = [
    {
      type: 'select',
      label: 'Period',
      name: 'period',
      options: periodOptions,
      placeholder: 'Select Period',
    },
    {
      type: 'search',
      label: 'Search',
      name: 'search',
      placeholder: 'Enter Search',
    },
    // Date range field will be conditionally included below
    {
      type: 'select',
      label: 'Department',
      name: 'department',
      options: departmentOptions,
      placeholder: 'Select Department',
    },
    {
      type: 'text',
      label: 'Other serach',
      name: 'other_search',
      placeholder: 'Enter Other serach',
    },
    {
      type: 'select',
      label: 'User',
      name: 'user',
      options: userOptions,
      placeholder: 'Select User',
    },
  ];

  // Insert date-range field if period is 'custom'
  if (filters.period === 'custom') {
    filterFields.splice(1, 0, {
      type: 'date-range',
      label: 'Date Range',
      name: 'dateRange',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    });
  }

  // Handle filter changes
  const handleApplyFilters = (values) => {
    let newFilters = { ...values };
    // If period is week/month, set dateRange automatically
    if (values.period === 'week') {
      newFilters.dateRange = getCurrentWeekRange();
    } else if (values.period === 'month') {
      newFilters.dateRange = getCurrentMonthRange();
    }
    setFilters(newFilters);

    // Apply filters to the data
    let filtered = [...staticData];

    // Search filter
    if (newFilters.search) {
      const searchTerm = newFilters.search.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.employee.toLowerCase().includes(searchTerm) ||
          item.department.toLowerCase().includes(searchTerm) ||
          item.shift.toLowerCase().includes(searchTerm) ||
          item.status.toLowerCase().includes(searchTerm)
      );
    }

    // Date range filter
    if (newFilters.dateRange && newFilters.dateRange.length === 2) {
      const startDate = new Date(newFilters.dateRange[0]);
      const endDate = new Date(newFilters.dateRange[1]);
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.date);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // Department filter
    if (newFilters.department) {
      filtered = filtered.filter(
        (item) =>
          item.department.toLowerCase() === newFilters.department.toLowerCase()
      );
    }

    // Other search filter
    if (newFilters.other_search) {
      const otherSearchTerm = newFilters.other_search.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.employee.toLowerCase().includes(otherSearchTerm) ||
          item.department.toLowerCase().includes(otherSearchTerm) ||
          item.shift.toLowerCase().includes(otherSearchTerm) ||
          item.hours.toLowerCase().includes(otherSearchTerm) ||
          item.status.toLowerCase().includes(otherSearchTerm)
      );
    }

    // User filter (assuming it filters by employee name)
    if (newFilters.user) {
      filtered = filtered.filter((item) =>
        item.employee.toLowerCase().includes(newFilters.user.toLowerCase())
      );
    }

    setFilteredData(filtered);
  };

  // Handle field change (especially for period)
  const handleFieldChange = (name, value) => {
    if (name === 'period') {
      let updatedFilters = { ...filters, period: value };
      if (value === 'custom') {
        updatedFilters.dateRange = [null, null];
      } else if (value === 'week') {
        updatedFilters.dateRange = getCurrentWeekRange();
      } else if (value === 'month') {
        updatedFilters.dateRange = getCurrentMonthRange();
      }
      setFilters(updatedFilters);
    } else {
      setFilters((prev) => ({ ...prev, [name]: value }));
    }
  };

  return (
    <Box className="report-main-container">
      <FilterCollapse
        fields={filterFields}
        onApply={handleApplyFilters}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />
      {/* Your report content goes here */}

      <Box className="report-table-container">
        <CommonTable
          columns={columns}
          data={filteredData}
          pageSize={10}
          actionMenuItems={menuItems}
        />
      </Box>
    </Box>
  );
}
