import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import './customdaterangepicker.scss';

const CustomDateRangePicker = ({
  value = [null, null],
  onChange,
  label = 'Date Range',
  placeholder = 'Select date range',
  className = '',
  disabled = false,
  format = 'MMM dd, yyyy',
}) => {
  return (
    <div className={`custom-date-range-picker ${className}`.trim()}>
      {label && <label className="field-label">{label}</label>}
      <DatePicker
        selectsRange
        startDate={value[0]}
        endDate={value[1]}
        onChange={onChange}
        dateFormat={format}
        placeholderText={placeholder}
        className="custom-date-range-input"
        disabled={disabled}
        isClearable
        clearButtonClassName="custom-date-range-clear-button"
        // monthsShown={2}
      />
    </div>
  );
};

export default CustomDateRangePicker;
