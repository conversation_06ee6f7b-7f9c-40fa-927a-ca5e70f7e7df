.custom-date-range-picker {
  display: flex;
  flex-direction: column;
  // gap: 6px;
  width: 100%;
  .custom-date-range-input {
    width: 100%;
    padding: 7px 12px;
    border: var(--field-border);
    border-radius: var(--field-radius);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-xs);
    background: var(--field-background);
    color: var(--text-color-black);
    transition: border-color 0.2s;
  }
  .custom-date-range-input:focus {
    border: var(--field-border-primary);
    outline: none;
  }
  .custom-date-range-input:disabled {
    background: var(--color-light-gray);
    color: var(--text-color-muted);
    cursor: not-allowed;
  }
  .custom-date-range-clear-button {
    padding-right: 12px;
    &::after {
      background: transparent;
      color: var(--icon-color-slate-gray);
      font-size: var(--font-size-xl);
    }
  }
}
